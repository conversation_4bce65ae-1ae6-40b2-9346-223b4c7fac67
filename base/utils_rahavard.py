from django.conf import settings

from datetime import datetime
from os import path, listdir, getenv
from re import match, sub
from typing import Any, Dict, List, Optional

from natsort import natsorted


YMD_REGEX = r'[0-9]{4}-[0-9]{2}-[0-9]{2}'
HMS_REGEX = r'[0-9]{2}:[0-9]{2}:[0-9]{2}'


def contains_ymd(string: str) -> bool:
    '''
    Check if a string contains a date in the format YYYY-MM-DD.

    This function uses a regular expression to determine if the input string contains a date in the format YYYY-MM-DD.

    Args:
        string (str): The input string to be checked.

    Returns:
        bool: True if the string contains a date in the format YYYY-MM-DD, False otherwise.

    Examples:
        >>> contains_ymd("Today's date is 2023-10-05.")
        True
        >>> contains_ymd('No date here!')
        False
        >>> contains_ymd('The event is on 2023-12-25.')
        True
        >>> contains_ymd('Date: 2023/10/05')
        False
    '''

    return match(f'^.*{YMD_REGEX}.*$', string) is not None

def is_ymd(string: str) -> bool:
    '''
    Check if a given string matches the Year-Month-Day (YMD) format.

    Args:
        string (str): The string to be checked.

    Returns:
        bool: True if the string matches the YMD format, False otherwise.

    Examples:
        >>> is_ymd('2023-10-05')
        True
        >>> is_ymd('05-10-2023')
        False
        >>> is_ymd('2023/10/05')
        False
        >>> is_ymd('20231005')
        False
    '''

    return match(f'^{YMD_REGEX}$', string) is not None

def starts_with_ymdhms(string: str) -> bool:
    '''
    Check if a string starts with a date and time in the format 'YYYY-MM-DD HH:MM:SS'.

    Args:
        string (str): The string to be checked.

    Returns:
        bool: True if the string starts with a date and time in the specified format, False otherwise.

    Examples:
        >>> starts_with_ymdhms('2023-10-05 12:34:56 Some event')
        True
        >>> starts_with_ymdhms('Some event 2023-10-05 12:34:56')
        False
        >>> starts_with_ymdhms('2023-10-05 12:34:56')
        False
        >>> starts_with_ymdhms('2023-10-05 12:34 Some event')
        False
    '''

    return match(f'^{YMD_REGEX} {HMS_REGEX} ', string) is not None

## ---------------------------------

def get_list_of_files(directory: str, extension: str) -> List[str]:
    '''
    Get a list of files in a directory with a specific extension, sorted naturally.

    Args:
        directory (str): The directory to search for files.
        extension (str): The file extension to filter by.

    Returns:
        list: A list of absolute file paths with the specified extension, sorted naturally.

    Examples:
        >>> get_list_of_files('/FOO/BAR/BAZ', 'txt')
        ['/FOO/BAR/BAZ/file1.txt', '/FOO/BAR/BAZ/file2.txt']

        >>> get_list_of_files('/FOO/BAR/BAZ', 'py')
        ['/FOO/BAR/BAZ/script1.py', '/FOO/BAR/BAZ/script2.py']

        >>> get_list_of_files('/non/existent/dir', 'txt')
        []

        >>> get_list_of_files('/FOO/BAR/BAZ', 'jpg')
        ['/FOO/BAR/BAZ/image1.jpg', '/FOO/BAR/BAZ/image2.jpg']
    '''

    if not path.exists(directory):
        return []

    return natsorted([
        path.abspath(path.join(directory, _))
        for _ in listdir(directory)

        if all([
            ## NOTE do NOT .{extension} -> {extension}
            _.endswith((f'.{extension}')),

            path.isfile(f'{directory}/{_}'),
        ])
    ])

def sort_dict(dictionary: Dict[Any, Any], based_on: str, reverse: bool) -> Dict[Any, Any]:
    '''
    Sort a dictionary based on its keys or values.

    Parameters:
        dictionary (Dict[Any, Any]): The dictionary to be sorted.
        based_on (str): The criteria to sort by, either 'key' or 'value'.
        reverse (bool): If True, sort in descending order, otherwise ascending.

    Returns:
        dict: A new dictionary sorted based on the specified criteria.

    Examples:
        >>> sort_dict({'b': 2, 'a': 1, 'c': 3}, based_on='key', reverse=False)
        {'a': 1, 'b': 2, 'c': 3}

        >>> sort_dict({'b': 2, 'a': 1, 'c': 3}, based_on='key', reverse=True)
        {'c': 3, 'b': 2, 'a': 1}

        >>> sort_dict({'b': 2, 'a': 1, 'c': 3}, based_on='value', reverse=False)
        {'a': 1, 'b': 2, 'c': 3}

        >>> sort_dict({'b': 2, 'a': 1, 'c': 3}, based_on='value', reverse=True)
        {'c': 3, 'b': 2, 'a': 1}
    '''

    if based_on == 'key':
        return dict(natsorted(dictionary.items(), reverse=reverse))

    if based_on == 'value':
        return dict(natsorted(dictionary.items(), key=lambda item: item[1], reverse=reverse))

    return dictionary

def to_tilda(text: str) -> str:
    '''
    Replaces the home directory path in the given text with a tilde (~).

    Args:
        text (str): The text in which to replace the home directory path.

    Returns:
        str: The text with the home directory path replaced by a tilde.

    Examples:
        >>> to_tilda('/home/<USER>/documents/file.txt')
        '~/documents/file.txt'
        >>> to_tilda('/home/<USER>/')
        '~/'
        >>> to_tilda('/home/<USER>/file.txt')
        '/home/<USER>/file.txt'
    '''
    return sub(getenv('HOME'), '~', text)

def abort(self, text: Optional[str] = None) -> None:
    print()
    if text:
        print(text)
    print('aborting...')
    print()

def get_command(full_path: str, drop_extention: bool = True) -> str:
    '''
    Extracts the command name from a given full path of a Django custom command.

    Args:
        full_path (str): The full path of the Django custom command file.
        drop_extention (bool, optional): If True, drops the file extension from the command name. Defaults to True.

    Returns:
        str: The command name extracted from the full path.

    Examples:
        >>> get_command('/Foo/BAR/BAZ/commands/parse-dns.py')
        'parse-dns'

        >>> get_command('/Foo/BAR/BAZ/commands/parse-dns.py', drop_extention=False)
        'parse-dns.py'
    '''

    base = path.basename(full_path)  ## parse-dns.py

    if drop_extention:
        root_base, _ = path.splitext(base)  ## parse-dns
        return root_base

    return base

def get_command_log_file(command: str) -> str:
    '''
    Examples:
        >>> get_command_log_file('live-parse')
        '/FOO/BAR/BAZ/live-parse.log'
    '''

    return f'{settings.PROJECT_LOGS_DIR}/{command}.log'

def save_log(self, command: str, host_name: str, dest_file: str, msg: str, echo: bool = True) -> None:
    '''
    Logs a message to a specified file and optionally prints it with colorized output.

    Args:
        command (str): The command that was executed.
        host_name (str): The name of the host where the command was executed.
        dest_file (str): The file path where the log should be saved.
        msg (str): The message to log.
        echo (bool, optional): If True, prints the message to the console with colorized output. Defaults to True.

    Examples:
        save_log(self, 'live-parse', 'abc-def.local', '/FOO/BAR/BAZ/live-parse.log', 'parse accomplished in 5 minutes')
    '''

    ymdhms = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    msg = to_tilda(msg)

    if echo:
        print(f"{host_name} {command} {ymdhms} {msg}")

    with open(dest_file, 'a') as opened:
        opened.write(f'{ymdhms} {msg}\n')
