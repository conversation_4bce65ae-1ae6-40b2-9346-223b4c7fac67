{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {# 1 #}
  {% create_id_for_htmx_indicator "tops" as id_for_htmx_indicator__top %}
  <div class="col-12">
    <div class="card custom-card shadow     h-100">

      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title">
          {% include '00-header-title.html' with mode="multi-date" ttl="Tops" daysago=date_to_show|days_ago %}
        </div>
        {# right #}
        <div class="d-flex align-items-center">
          {% if page_has_scrollable_tables %}
            {% include '00-scrollable-table-icon.html' %}
          {% endif %}
        </div>
      </div>

      <div class="card-body p-0">
        <div class="low_height">
          <table class="table table-striped table-borderless table-sm sortable">
            <thead class="{{class_for_thead}}">
              <tr>
                {# ID #}
                <th scope="col" class="sorttable_numeric"></th>

                {# __TRANSFER_REPORT_OR_DURATION_REPORT__ #}
                {% if table_slug == "transfer-report" or table_slug == "duration-report" %}
                  <th scope="col">IP</th>
                {% else %}
                  <th scope="col">{{column_name}}</th>
                {% endif %}

                {# extras #}
                {% if table_slug == "source-ip" or table_slug == "transfer-report" or table_slug == "duration-report" %}
                  <th scope="col">Computer Name</th>
                  <th scope="col">Real Name</th>
                  {# <th scope="col">MAC Address</th> #}
                {% comment %}
                {% elif table_slug == "mac-address" %}
                  <th scope="col">Computer Name</th>
                  <th scope="col">Real Name</th>
                  <th scope="col">IP</th>
                {% endcomment %}
                {% endif %}

                {# transfer/duration/count #}
                {% if table_slug == "transfer-report" %}
                  <th scope="col" class="sorttable_numeric">Transfer</th>
                {% elif table_slug == "duration-report" %}
                  <th scope="col" class="sorttable_numeric">Duration</th>
                {% else %}
                  <th scope="col" class="sorttable_numeric">Count</th>
                {% endif %}

                {# percent #}
                <th scope="col" class="sorttable_numeric">Percent</th>
              </tr>
            </thead>
            <tbody>
              {% include '00-tabular-category--rows.html' with section="tops" by_date_ymd="None" id_for_htmx_indicator=id_for_htmx_indicator__top     app_slug=app_slug windowsserver_name=windowsserver_name windowsserver_category=windowsserver_category %}
            </tbody>
          </table>

          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator__top %}
        </div>
      </div>

      <div
        id="htmx_fade_in"
        class="card-footer"

        hx-include="#dropdown_form"
        {% if app_slug == "dhcp" %}
          hx-get="{% url "dhcp-tabular-category-url" table_slug %}"
        {% elif app_slug == "dns" %}
          hx-get="{% url "dns-tabular-category-url" table_slug %}"
        {% elif app_slug == "filterlog" %}
          hx-get="{% url "filterlog-tabular-category-url" table_slug %}"
        {% elif app_slug == "snort" %}
          hx-get="{% url "snort-tabular-category-url" table_slug %}"
        {% elif app_slug == "squid" %}
          hx-get="{% url "squid-tabular-category-url" table_slug %}"
        {% elif app_slug == "usernotice" %}
          hx-get="{% url "usernotice-tabular-category-url" table_slug %}"
        {% elif app_slug == "vpnserver" %}
          hx-get="{% url "vpnserver-tabular-category-url" table_slug %}"
        {% elif app_slug == "windowsserver" %}
          hx-get="{% url "windowsserver-tabular-category-url" windowsserver_name table_slug %}"
        {% endif %}
        hx-vals='{"section": "tops", "overview": "true"}'
        hx-trigger="intersect once"
        hx-swap="outerHTML"
        hx-indicator="#overview-tops"
      >
        {% include '00-htmx-indicator.html' with id_for_htmx_indicator="overview-tops" %}
      </div>

    </div>
  </div>
  {# 1 / #}

  {% include '00-by-date.html' %}

  {% for ymd in date_range %}
    {% create_id_for_htmx_indicator "by-date"           table_slug ymd as id_for_htmx_indicator__by_date %}
    {% create_id_for_htmx_indicator "by-date-overview-" table_slug ymd as id_for_htmx_indicator__by_date__overview %}

    <div class="{% if table_slug == "source-ip" or table_slug == "transfer-report" or table_slug == "duration-report" %}col-xxl-6 col-12{% else %}col-xxl-4 col-xl-6 col-12{% endif %}" id="category_div_{{forloop.counter}}">

      <div class="card custom-card shadow     h-100">

        <div class="card-header justify-content-between">
          {# left #}
          <div class="card-title">
            {% include '00-header-title.html' with mode="single-date" ttl=ymd daysago=ymd|days_ago %}
          </div>
          {# right #}
          <div class="d-flex align-items-center">
            {% if page_has_scrollable_tables %}
              {% include '00-scrollable-table-icon.html' %}
            {% endif %}
          </div>
        </div>

        <div class="card-body p-0">
          <div class="low_height">
            <table class="table table-striped table-borderless table-sm sortable">
              <thead class="{{class_for_thead}}">
                <tr>
                  {# ID #}
                  <th scope="col" class="sorttable_numeric"></th>

                  {# __TRANSFER_REPORT_OR_DURATION_REPORT__ #}
                  {% if table_slug == "transfer-report" or table_slug == "duration-report" %}
                    <th scope="col">IP</th>
                  {% else %}
                    <th scope="col">{{column_name}}</th>
                  {% endif %}

                  {% if table_slug == "source-ip" or table_slug == "transfer-report" or table_slug == "duration-report" %}
                    <th scope="col">Computer Name</th>
                    <th scope="col">Real Name</th>
                    <th scope="col">MAC Address</th>
                  {% elif table_slug == "mac-address" %}
                    <th scope="col">Computer Name</th>
                    <th scope="col">Real Name</th>
                    <th scope="col">IP</th>
                  {% endif %}

                  {# transfer/duration/count #}
                  {% if table_slug == "transfer-report" %}
                    <th scope="col" class="sorttable_numeric">Transfer</th>
                  {% elif table_slug == "duration-report" %}
                    <th scope="col" class="sorttable_numeric">Duration</th>
                  {% else %}
                    <th scope="col" class="sorttable_numeric">Count</th>
                  {% endif %}

                  {# percent #}
                  <th scope="col" class="sorttable_numeric">Percent</th>
                </tr>
              </thead>
              <tbody>
                {% include '00-tabular-category--rows.html' with section="by-date" by_date_ymd=ymd id_for_htmx_indicator=id_for_htmx_indicator__by_date     app_slug=app_slug windowsserver_name=windowsserver_name windowsserver_category=windowsserver_category %}
              </tbody>
            </table>

            {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator__by_date %}
          </div>
        </div>

        <div
          id="htmx_fade_in"
          class="card-footer"

          hx-include="#dropdown_form"
          {% if app_slug == "dhcp" %}
            hx-get="{% url "dhcp-tabular-category-url" table_slug %}"
          {% elif app_slug == "dns" %}
            hx-get="{% url "dns-tabular-category-url" table_slug %}"
          {% elif app_slug == "filterlog" %}
            hx-get="{% url "filterlog-tabular-category-url" table_slug %}"
          {% elif app_slug == "snort" %}
            hx-get="{% url "snort-tabular-category-url" table_slug %}"
          {% elif app_slug == "squid" %}
            hx-get="{% url "squid-tabular-category-url" table_slug %}"
          {% elif app_slug == "usernotice" %}
            hx-get="{% url "usernotice-tabular-category-url" table_slug %}"
          {% elif app_slug == "vpnserver" %}
            hx-get="{% url "vpnserver-tabular-category-url" table_slug %}"
          {% elif app_slug == "windowsserver" %}
            hx-get="{% url "windowsserver-tabular-category-url" windowsserver_name table_slug %}"
          {% endif %}
          hx-vals='{"section": "by-date", "overview": "true", "by-date-ymd": "{{ymd}}"}'
          hx-trigger="intersect once"
          hx-swap="outerHTML"
          hx-indicator="#{{id_for_htmx_indicator__by_date__overview}}"
        >
          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator__by_date__overview %}
        </div>

      </div>
    </div>
  {% endfor %}
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
