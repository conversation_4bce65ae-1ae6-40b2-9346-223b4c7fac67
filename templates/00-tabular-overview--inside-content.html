{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% for t in all_tables %}
    {% create_id_for_htmx_indicator t.slug             as id_for_htmx_indicator %}
    {% create_id_for_htmx_indicator t.slug "-overview" as id_for_htmx_indicator__overview %}

    <div class="col-xxl-4 col-xl-6 col-12">
      <div class="card custom-card shadow     h-100">

        <div class="card-header justify-content-between">
          {# left #}
          <div class="card-title">
            {% include '00-header-title.html' with mode="multi-date" ttl=t.column_name daysago=date_to_show|days_ago %}
          </div>
          {# right #}
          <div class="d-flex align-items-center">
            {% if page_has_scrollable_tables %}
              {% include '00-scrollable-table-icon.html' %}
            {% endif %}
          </div>
        </div>

        <div class="card-body p-0">
          <div class="low_height">
            <table class="table table-striped table-borderless table-sm sortable">
              <thead class="{{class_for_thead}}">
                <tr>
                  {# columns also in templates/00-combined-overview--inside-content.html #}

                  {# ID #}
                  <th scope="col" class="sorttable_numeric"></th>

                  {# __TRANSFER_REPORT_OR_DURATION_REPORT__ #}
                  {% if t.slug == "transfer-report" or t.slug == "duration-report" %}
                    <th scope="col">IP</th>
                  {% else %}
                    <th scope="col">{{t.column_name}}</th>
                  {% endif %}

                  {# extras #}
                  {% if t.slug == "source-ip" or t.slug == "transfer-report" or t.slug == "duration-report" %}
                    <th scope="col">Computer Name</th>
                    <th scope="col">Real Name</th>
                  {% endif %}

                  {# transfer/duration/count #}
                  {% if t.slug == "transfer-report" %}
                    <th scope="col" class="sorttable_numeric">Transfer</th>
                  {% elif t.slug == "duration-report" %}
                    <th scope="col" class="sorttable_numeric">Duration</th>
                  {% else %}
                    <th scope="col" class="sorttable_numeric">Count</th>
                  {% endif %}

                  {# percent #}
                  <th scope="col" class="sorttable_numeric">Percent</th>
                </tr>
              </thead>
              <tbody>
                {% include '00-tabular-overview--rows.html' with table_slug=t.slug id_for_htmx_indicator=id_for_htmx_indicator     app_slug=app_slug windowsserver_name=windowsserver_name windowsserver_category=windowsserver_category geo_mode=geo_mode %}
              </tbody>
            </table>

            {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
          </div>
        </div>

        <div
          id="htmx_fade_in"
          class="card-footer"

          hx-include="#dropdown_form"
          {% if app_slug == "dhcp" %}
            hx-get="{% url "dhcp-tabular-overview-url" %}"
          {% elif app_slug == "dns" %}
            hx-get="{% url "dns-tabular-overview-url" %}"
          {% elif app_slug == "filterlog" %}
            hx-get="{% url "filterlog-tabular-overview-url" %}"
          {% elif app_slug == "snort" %}
            hx-get="{% url "snort-tabular-overview-url" %}"
          {% elif app_slug == "squid" %}
            hx-get="{% url "squid-tabular-overview-url" %}"
          {% elif app_slug == "usernotice" %}
            hx-get="{% url "usernotice-tabular-overview-url" %}"
          {% elif app_slug == "vpnserver" %}
            hx-get="{% url "vpnserver-tabular-overview-url" %}"
          {% elif app_slug == "windowsserver" %}
            hx-get="{% url "windowsserver-tabular-overview-url" windowsserver_name %}"
          {% elif app_slug == "geolocation" %}
            hx-get="{% url "geolocation-tabular-overview-url" geo_mode %}"
          {% endif %}
          hx-vals='{"table-slug": "{{t.slug}}", "overview": "true"}'
          hx-trigger="intersect once"
          hx-swap="outerHTML"
          hx-indicator="#{{id_for_htmx_indicator__overview}}"
        >
          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator__overview %}
        </div>

      </div>
    </div>
  {% endfor %}
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
